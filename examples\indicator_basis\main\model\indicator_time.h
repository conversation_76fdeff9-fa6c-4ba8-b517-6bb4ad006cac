#ifndef INDICATOR_TIME_H
#define INDICATOR_TIME_H

#include "config.h"
#include "view_data.h"
#include <time.h>

#ifdef __cplusplus
extern "C" {
#endif


//ntp sync
int indicator_time_init(void);

// set TZ
int indicator_time_net_zone_set( char *p);

// set IST time from API
int indicator_time_set_ist(void);

// enable/disable IST timezone forcing
void indicator_time_force_ist(bool enable);

#ifdef __cplusplus
}
#endif

#endif
