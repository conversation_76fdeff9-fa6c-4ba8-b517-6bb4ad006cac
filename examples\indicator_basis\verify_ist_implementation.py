#!/usr/bin/env python3
"""
Verification script for IST time implementation
Checks for common issues in the modified code
"""

import os
import re

def check_file_exists(filepath):
    """Check if file exists"""
    if os.path.exists(filepath):
        print(f"✓ {filepath} exists")
        return True
    else:
        print(f"✗ {filepath} missing")
        return False

def check_includes(filepath):
    """Check if required includes are present"""
    required_includes = [
        '#include "esp_http_client.h"',
        '#include "cJSON.h"',
        '#include <time.h>',
        '#include <sys/time.h>',
        '#include <string.h>'
    ]
    
    if not os.path.exists(filepath):
        return False
        
    with open(filepath, 'r') as f:
        content = f.read()
    
    missing_includes = []
    for include in required_includes:
        if include not in content:
            missing_includes.append(include)
    
    if missing_includes:
        print(f"✗ Missing includes in {filepath}:")
        for inc in missing_includes:
            print(f"  - {inc}")
        return False
    else:
        print(f"✓ All required includes present in {filepath}")
        return True

def check_functions(filepath):
    """Check if required functions are implemented"""
    required_functions = [
        '_ist_http_event_handler',
        '__ist_time_parse',
        '__fetch_ist_time',
        '__set_ist_time',
        'indicator_time_set_ist',
        'indicator_time_force_ist'
    ]
    
    if not os.path.exists(filepath):
        return False
        
    with open(filepath, 'r') as f:
        content = f.read()
    
    missing_functions = []
    for func in required_functions:
        if func not in content:
            missing_functions.append(func)
    
    if missing_functions:
        print(f"✗ Missing functions in {filepath}:")
        for func in missing_functions:
            print(f"  - {func}")
        return False
    else:
        print(f"✓ All required functions present in {filepath}")
        return True

def check_api_url(filepath):
    """Check if WorldTimeAPI URL is correct"""
    if not os.path.exists(filepath):
        return False
        
    with open(filepath, 'r') as f:
        content = f.read()
    
    expected_url = "http://worldtimeapi.org/api/timezone/Asia/Kolkata"
    if expected_url in content:
        print(f"✓ Correct WorldTimeAPI URL found in {filepath}")
        return True
    else:
        print(f"✗ WorldTimeAPI URL not found or incorrect in {filepath}")
        return False

def check_timezone_setting(filepath):
    """Check if IST timezone is set correctly"""
    if not os.path.exists(filepath):
        return False
        
    with open(filepath, 'r') as f:
        content = f.read()
    
    ist_timezone = "IST-5:30"
    if ist_timezone in content:
        print(f"✓ IST timezone setting found in {filepath}")
        return True
    else:
        print(f"✗ IST timezone setting not found in {filepath}")
        return False

def check_header_declarations(filepath):
    """Check if header file has correct function declarations"""
    required_declarations = [
        'int indicator_time_set_ist(void);',
        'void indicator_time_force_ist(bool enable);'
    ]
    
    if not os.path.exists(filepath):
        return False
        
    with open(filepath, 'r') as f:
        content = f.read()
    
    missing_declarations = []
    for decl in required_declarations:
        if decl not in content:
            missing_declarations.append(decl)
    
    if missing_declarations:
        print(f"✗ Missing declarations in {filepath}:")
        for decl in missing_declarations:
            print(f"  - {decl}")
        return False
    else:
        print(f"✓ All required declarations present in {filepath}")
        return True

def main():
    """Main verification function"""
    print("IST Time Implementation Verification")
    print("=" * 40)
    
    base_path = "main/model/"
    c_file = base_path + "indicator_time.c"
    h_file = base_path + "indicator_time.h"
    
    all_checks_passed = True
    
    # Check file existence
    all_checks_passed &= check_file_exists(c_file)
    all_checks_passed &= check_file_exists(h_file)
    
    # Check C file
    if os.path.exists(c_file):
        all_checks_passed &= check_includes(c_file)
        all_checks_passed &= check_functions(c_file)
        all_checks_passed &= check_api_url(c_file)
        all_checks_passed &= check_timezone_setting(c_file)
    
    # Check header file
    if os.path.exists(h_file):
        all_checks_passed &= check_header_declarations(h_file)
    
    print("\n" + "=" * 40)
    if all_checks_passed:
        print("✓ All verification checks passed!")
        print("The IST time implementation appears to be correct.")
        print("\nNext steps:")
        print("1. Build the project: idf.py build")
        print("2. Flash to device: idf.py flash monitor")
        print("3. Connect to WiFi and verify IST time is displayed")
    else:
        print("✗ Some verification checks failed!")
        print("Please review the implementation and fix the issues.")
    
    return all_checks_passed

if __name__ == "__main__":
    main()
