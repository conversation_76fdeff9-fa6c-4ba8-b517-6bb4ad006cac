# IST Time Implementation for SenseCAP Indicator

## Overview
This document describes the modifications made to the `indicator_basis` example to fetch and display Indian Standard Time (IST) correctly on boot.

## Problem
The original implementation relied on IP-based geolocation to determine timezone, which could be inaccurate and didn't guarantee IST time display.

## Solution
Modified the time system to:
1. Force IST timezone (UTC+5:30) by default
2. Fetch current IST time from WorldTimeAPI.org on WiFi connection
3. Set system time directly from the API response
4. Prevent other modules from overriding the IST timezone setting

## Files Modified

### 1. `main/model/indicator_time.h`
- Added `indicator_time_set_ist()` function declaration
- Added `indicator_time_force_ist(bool enable)` function declaration

### 2. `main/model/indicator_time.c`
- Added HTTP client includes and time-related includes
- Added IST response buffer and force IST flag
- Added HTTP event handler for IST API calls
- Added IST time parsing function
- Added IST time fetching function from WorldTimeAPI
- Added IST time setting function
- Modified WiFi event handler to fetch IST time on connection
- Modified default configuration to set IST timezone
- Modified `indicator_time_net_zone_set()` to respect IST forcing
- Added public function to enable/disable IST forcing

## Key Functions Added

### `__fetch_ist_time(time_t *ist_time)`
Fetches current IST time from `http://worldtimeapi.org/api/timezone/Asia/Kolkata`
- Uses ESP HTTP client
- Parses JSON response to extract unixtime
- Returns 0 on success, -1 on failure

### `__set_ist_time(void)`
Sets system timezone to IST and fetches current time from API
- Sets timezone to "IST-5:30"
- Calls `__fetch_ist_time()` to get current time
- Sets system time using `settimeofday()`
- Logs formatted time for verification

### `indicator_time_set_ist(void)`
Public function to manually trigger IST time setting
- Calls `__set_ist_time()`
- Updates internal timezone string
- Triggers display update event

### `indicator_time_force_ist(bool enable)`
Public function to enable/disable IST timezone forcing
- Controls whether other modules can override IST setting
- Useful for debugging or allowing manual timezone changes

## Behavior Changes

### On Boot
1. Default timezone is set to IST (UTC+5:30)
2. Time display shows IST timezone even before WiFi connection
3. System waits for WiFi connection to fetch accurate time

### On WiFi Connection
1. Automatically fetches current IST time from WorldTimeAPI
2. Sets system time to the fetched IST time
3. Updates time display with accurate IST time
4. Falls back to NTP if API call fails

### Timezone Protection
- Other modules (like city detection) cannot override IST timezone
- IST forcing can be disabled if needed using `indicator_time_force_ist(false)`

## API Used
- **WorldTimeAPI**: `http://worldtimeapi.org/api/timezone/Asia/Kolkata`
- **Response Format**: JSON with fields like `unixtime`, `datetime`, `timezone`
- **No Authentication**: Free API, no API key required
- **Reliability**: Provides accurate IST time with timezone information

## Testing Instructions

1. **Build the project**:
   ```bash
   cd examples/indicator_basis
   idf.py build
   ```

2. **Flash to device**:
   ```bash
   idf.py flash monitor
   ```

3. **Expected Behavior**:
   - Device boots with IST timezone set
   - After WiFi connection, logs should show:
     - "WiFi connected, setting IST time..."
     - "IST API Response: {JSON response}"
     - "System time set to IST: {timestamp}"
     - "Current IST time: {formatted time}"
   - Display should show correct IST time

4. **Verification**:
   - Check serial monitor for IST time logs
   - Verify display shows IST time (UTC+5:30)
   - Compare with actual IST time from reliable source

## Fallback Mechanism
If WorldTimeAPI is unavailable:
- System falls back to original NTP synchronization
- Timezone remains set to IST
- Time accuracy depends on NTP servers

## Configuration Options
- `force_ist_timezone`: Controls IST timezone protection (default: true)
- Can be modified at runtime using `indicator_time_force_ist()`

## Dependencies
- ESP HTTP Client component
- cJSON library for JSON parsing
- Standard time libraries (time.h, sys/time.h)
- Existing WiFi and event handling infrastructure
