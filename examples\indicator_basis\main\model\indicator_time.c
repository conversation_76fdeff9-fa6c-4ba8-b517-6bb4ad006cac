#include "indicator_time.h"
#include "esp_sntp.h"
#include "freertos/semphr.h"
#include<stdlib.h>
#include "nvs.h"
#include "esp_timer.h"
#include "esp_http_client.h"
#include "cJSON.h"
#include "esp_log.h"
#include <time.h>
#include <sys/time.h>
#include <string.h>

#define TIME_CFG_STORAGE  "time-cfg"
#define MAX_HTTP_OUTPUT_BUFFER 2048

struct indicator_time
{
    struct view_data_time_cfg cfg;
    char net_zone[64];
};

static const char *TAG = "time";

static struct indicator_time __g_time_model;
static SemaphoreHandle_t       __g_data_mutex;
static char ist_response_buffer[MAX_HTTP_OUTPUT_BUFFER];
static bool force_ist_timezone = true;  // Flag to force IST timezone

static esp_timer_handle_t   view_update_timer_handle;

// HTTP event handler for IST time API
static esp_err_t _ist_http_event_handler(esp_http_client_event_t *evt)
{
    static char *output_buffer;  // Buffer to store response of http request from event handler
    static int output_len;       // Stores number of bytes read
    switch(evt->event_id) {
        case HTTP_EVENT_ERROR:
            ESP_LOGD(TAG, "HTTP_EVENT_ERROR");
            break;
        case HTTP_EVENT_ON_CONNECTED:
            ESP_LOGD(TAG, "HTTP_EVENT_ON_CONNECTED");
            break;
        case HTTP_EVENT_HEADER_SENT:
            ESP_LOGD(TAG, "HTTP_EVENT_HEADER_SENT");
            break;
        case HTTP_EVENT_ON_HEADER:
            ESP_LOGD(TAG, "HTTP_EVENT_ON_HEADER, key=%s, value=%s", evt->header_key, evt->header_value);
            break;
        case HTTP_EVENT_ON_DATA:
            ESP_LOGD(TAG, "HTTP_EVENT_ON_DATA, len=%d", evt->data_len);
            if (!esp_http_client_is_chunked_response(evt->client)) {
                // If user_data buffer is configured, copy the response into the buffer
                if (evt->user_data) {
                    memcpy(evt->user_data + output_len, evt->data, evt->data_len);
                    output_len += evt->data_len;
                }
            }
            break;
        case HTTP_EVENT_ON_FINISH:
            ESP_LOGD(TAG, "HTTP_EVENT_ON_FINISH");
            output_len = 0;
            break;
        case HTTP_EVENT_DISCONNECTED:
            ESP_LOGI(TAG, "HTTP_EVENT_DISCONNECTED");
            output_len = 0;
            break;
        case HTTP_EVENT_REDIRECT:
            ESP_LOGD(TAG, "HTTP_EVENT_REDIRECT");
            break;
    }
    return ESP_OK;
}

// Parse IST time from WorldTimeAPI JSON response
static int __ist_time_parse(const char *json_str, time_t *ist_time)
{
    cJSON *root = NULL;
    cJSON *datetime_item = NULL;
    cJSON *unixtime_item = NULL;
    int ret = -1;

    root = cJSON_Parse(json_str);
    if (root == NULL) {
        ESP_LOGE(TAG, "Failed to parse IST time JSON");
        return -1;
    }

    // Try to get unixtime first (most reliable)
    unixtime_item = cJSON_GetObjectItem(root, "unixtime");
    if (unixtime_item != NULL && cJSON_IsNumber(unixtime_item)) {
        *ist_time = (time_t)unixtime_item->valueint;
        ESP_LOGI(TAG, "IST unixtime: %ld", *ist_time);
        ret = 0;
    } else {
        // Fallback to parsing datetime string
        datetime_item = cJSON_GetObjectItem(root, "datetime");
        if (datetime_item != NULL && cJSON_IsString(datetime_item)) {
            ESP_LOGI(TAG, "IST datetime: %s", datetime_item->valuestring);
            // For now, we'll rely on unixtime. If needed, we can add datetime parsing later
            ESP_LOGW(TAG, "Unixtime not available, datetime parsing not implemented");
            ret = -1;
        }
    }

    cJSON_Delete(root);
    return ret;
}

// Fetch current IST time from WorldTimeAPI
static int __fetch_ist_time(time_t *ist_time)
{
    memset(ist_response_buffer, 0, sizeof(ist_response_buffer));

    esp_http_client_config_t config = {
        .url = "http://worldtimeapi.org/api/timezone/Asia/Kolkata",
        .event_handler = _ist_http_event_handler,
        .user_data = ist_response_buffer,
        .disable_auto_redirect = true,
        .timeout_ms = 10000,
    };

    esp_http_client_handle_t client = esp_http_client_init(&config);
    if (client == NULL) {
        ESP_LOGE(TAG, "Failed to initialize HTTP client for IST time");
        return -1;
    }

    esp_err_t err = esp_http_client_perform(client);
    if (err == ESP_OK) {
        int status_code = esp_http_client_get_status_code(client);
        ESP_LOGI(TAG, "IST API HTTP Status = %d, content_length = %lld",
                status_code, esp_http_client_get_content_length(client));

        if (status_code == 200) {
            ESP_LOGI(TAG, "IST API Response: %s", ist_response_buffer);
            esp_http_client_cleanup(client);
            return __ist_time_parse(ist_response_buffer, ist_time);
        } else {
            ESP_LOGE(TAG, "IST API returned status code: %d", status_code);
        }
    } else {
        ESP_LOGE(TAG, "IST API HTTP request failed: %s", esp_err_to_name(err));
    }

    esp_http_client_cleanup(client);
    return -1;
}

// Set IST timezone and fetch current IST time
static int __set_ist_time(void)
{
    // First, set the timezone to IST
    setenv("TZ", "IST-5:30", 1);
    tzset();
    ESP_LOGI(TAG, "Timezone set to IST (UTC+5:30)");

    // Fetch current IST time from API
    time_t ist_time;
    if (__fetch_ist_time(&ist_time) == 0) {
        struct timeval tv = { .tv_sec = ist_time, .tv_usec = 0 };
        if (settimeofday(&tv, NULL) == 0) {
            ESP_LOGI(TAG, "System time set to IST: %ld", ist_time);

            // Log the formatted time for verification
            struct tm timeinfo;
            localtime_r(&ist_time, &timeinfo);
            char strftime_buf[64];
            strftime(strftime_buf, sizeof(strftime_buf), "%Y-%m-%d %H:%M:%S %Z", &timeinfo);
            ESP_LOGI(TAG, "Current IST time: %s", strftime_buf);

            return 0;
        } else {
            ESP_LOGE(TAG, "Failed to set system time");
        }
    } else {
        ESP_LOGE(TAG, "Failed to fetch IST time from API");
    }

    return -1;
}

static void __time_cfg_set(struct view_data_time_cfg *p_cfg )
{
    xSemaphoreTake(__g_data_mutex, portMAX_DELAY);
    memcpy( &__g_time_model.cfg, p_cfg, sizeof(struct view_data_time_cfg));
    xSemaphoreGive(__g_data_mutex);
}

static void __time_cfg_get(struct view_data_time_cfg *p_cfg )
{
    xSemaphoreTake(__g_data_mutex, portMAX_DELAY);
    memcpy(p_cfg, &__g_time_model.cfg, sizeof(struct view_data_time_cfg));
    xSemaphoreGive(__g_data_mutex);
}


static void __time_cfg_save(struct view_data_time_cfg *p_cfg )
{
    esp_err_t ret = 0;
    ret = indicator_storage_write(TIME_CFG_STORAGE, (void *)p_cfg, sizeof(struct view_data_time_cfg));
    if( ret != ESP_OK ) {
        ESP_LOGI(TAG, "cfg write err:%d", ret);
    } else {
        ESP_LOGI(TAG, "cfg write successful");
    }
}

static void __time_cfg_print(struct view_data_time_cfg *p_cfg )
{
    printf( "time_format_24:%d, auto_update:%d, time:%d, auto_update_zone:%d, zone:%d, daylight:%d\n",  \
      (bool) p_cfg->time_format_24, (bool)p_cfg->auto_update, (long)p_cfg->time, (bool)p_cfg->auto_update_zone, (int8_t)p_cfg->zone, (bool)p_cfg->daylight);
}


static void __time_sync_notification_cb(struct timeval *tv)
{
    ESP_LOGI("ntp", "Notification of a time synchronization event");
    struct view_data_time_cfg cfg;
    __time_cfg_get(&cfg);
    bool time_format_24 = cfg.time_format_24;
    esp_event_post_to(view_event_handle, VIEW_EVENT_BASE, VIEW_EVENT_TIME, &time_format_24, sizeof(time_format_24), portMAX_DELAY);
}

static void __time_set(time_t time)
{
    struct tm tm = {4, 14, 3, 19, 0, 138, 0, 0, 0};
    struct timeval timestamp = { time, 0 };
    settimeofday(&timestamp, NULL);
}

static void __time_sync_enable(void)
{
    sntp_init();
}

static void __time_sync_stop(void)
{
    sntp_stop();
}

static void __time_zone_set(struct view_data_time_cfg *p_cfg)
{
    if ( !p_cfg->auto_update_zone) {
        
        int8_t zone = p_cfg->zone;
        char zone_str[32];

        if( p_cfg->daylight) {
            zone -=1; //todo
        }
        if( zone >= 0) {
            snprintf(zone_str, sizeof(zone_str) - 1, "UTC-%d", zone);
        } else {
            snprintf(zone_str, sizeof(zone_str) - 1, "UTC+%d", 0 - zone);
        }
        setenv("TZ", zone_str, 1);
    } else {

        char net_zone[64] = {0};
        xSemaphoreTake(__g_data_mutex, portMAX_DELAY);
        memcpy(net_zone, &__g_time_model.net_zone, sizeof(net_zone));
        xSemaphoreGive(__g_data_mutex);

        if( strlen(net_zone) > 0 ) {
            setenv("TZ", net_zone, 1);
        }
    }
}

static void __time_cfg(struct view_data_time_cfg *p_cfg, bool set_time)
{
    if( p_cfg->auto_update ) {
        __time_sync_enable();
        __time_zone_set(p_cfg); 
    } else {
        __time_sync_stop();
        struct timeval timestamp = { p_cfg->time, 0 };
        if( set_time ) {
            settimeofday(&timestamp, NULL);
        }
    }
}

static void __time_view_update_callback(void* arg)
{
    static int last_min = 60;
    time_t now = 0;
    struct tm timeinfo = { 0 };
    time(&now);
    localtime_r(&now, &timeinfo);
    if( timeinfo.tm_min != last_min) {
        last_min = timeinfo.tm_min;
        
        struct view_data_time_cfg cfg;
        __time_cfg_get(&cfg);
        bool time_format_24 = cfg.time_format_24;
        esp_event_post_to(view_event_handle, VIEW_EVENT_BASE, VIEW_EVENT_TIME, &time_format_24, sizeof(time_format_24), portMAX_DELAY);
        ESP_LOGI(TAG, "need update time view");
        char strftime_buf[64];
        strftime(strftime_buf, sizeof(strftime_buf), "%c", &timeinfo);
        ESP_LOGI(TAG, "%s", strftime_buf);
    }
}

static __time_view_update_init(void)
{
    const esp_timer_create_args_t timer_args = {
            .callback = &__time_view_update_callback,
            /* argument specified here will be passed to timer callback function */
            .arg = (void*) view_update_timer_handle,
            .name = "time update"
    };
    ESP_ERROR_CHECK( esp_timer_create(&timer_args, &view_update_timer_handle));
    ESP_ERROR_CHECK(esp_timer_start_periodic(view_update_timer_handle, 1000000)); //1s
}


static void __view_event_handler(void* handler_args, esp_event_base_t base, int32_t id, void* event_data)
{
    switch (id)
    {
        case VIEW_EVENT_TIME_CFG_APPLY: {
            struct view_data_time_cfg * p_cfg = (struct view_data_time_cfg *)event_data;
            ESP_LOGI(TAG, "event: VIEW_EVENT_TIME_CFG_APPLY");
            __time_cfg_print(p_cfg);
            __time_cfg_set(p_cfg);
            __time_cfg_save(p_cfg);
            __time_cfg(p_cfg, p_cfg->set_time);  //config;

            bool time_format_24 = p_cfg->time_format_24;
            esp_event_post_to(view_event_handle, VIEW_EVENT_BASE, VIEW_EVENT_TIME, &time_format_24, sizeof(time_format_24), portMAX_DELAY);
            break;
        }
        case VIEW_EVENT_WIFI_ST: {
            static bool first = true;
            ESP_LOGI(TAG, "event: VIEW_EVENT_WIFI_ST");
            struct view_data_wifi_st *p_st = ( struct view_data_wifi_st *)event_data;
            if( p_st->is_network) {

                if( !first) {
                    break;
                }
                first = false;

                ESP_LOGI(TAG, "WiFi connected, setting IST time...");

                // Set IST time from API
                if (__set_ist_time() == 0) {
                    ESP_LOGI(TAG, "IST time set successfully");

                    // Update the net_zone to IST
                    xSemaphoreTake(__g_data_mutex, portMAX_DELAY);
                    strcpy(__g_time_model.net_zone, "IST-5:30");
                    xSemaphoreGive(__g_data_mutex);

                    // Trigger time display update
                    struct view_data_time_cfg cfg;
                    __time_cfg_get(&cfg);
                    bool time_format_24 = cfg.time_format_24;
                    esp_event_post_to(view_event_handle, VIEW_EVENT_BASE, VIEW_EVENT_TIME, &time_format_24, sizeof(time_format_24), portMAX_DELAY);
                } else {
                    ESP_LOGW(TAG, "Failed to set IST time from API, falling back to NTP");
                    // Fallback to original NTP sync if API fails
                    struct view_data_time_cfg cfg;
                    __time_cfg_get(&cfg);
                    if( cfg.auto_update ) {
                        __time_sync_stop();
                        __time_sync_enable();
                    }
                }
            }
        }
    default:
        break;
    }
}


static void __time_cfg_restore(void)
{

    esp_err_t ret = 0;
    struct view_data_time_cfg cfg;
    
    size_t len = sizeof(cfg);
    
    ret = indicator_storage_read(TIME_CFG_STORAGE, (void *)&cfg, &len);
    if( ret == ESP_OK  && len== (sizeof(cfg)) ) {
        ESP_LOGI(TAG, "cfg read successful");
        __time_cfg_set(&cfg);
    } else {
        // err or not find
        if( ret == ESP_ERR_NVS_NOT_FOUND) {
            ESP_LOGI(TAG, "cfg not find");
        }else {
            ESP_LOGI(TAG, "cfg read err:%d", ret);
        }

        cfg.auto_update = true;
        cfg.auto_update_zone = true;
        cfg.daylight = false;  // IST doesn't use daylight saving
        cfg.time_format_24 = true;
        cfg.zone = 5;  // IST is UTC+5:30, but we'll set timezone string directly
        cfg.time = 0;
        __time_cfg_set(&cfg);

        // Set IST timezone by default
        xSemaphoreTake(__g_data_mutex, portMAX_DELAY);
        strcpy(__g_time_model.net_zone, "IST-5:30");
        xSemaphoreGive(__g_data_mutex);
        setenv("TZ", "IST-5:30", 1);
        tzset();
        ESP_LOGI(TAG, "Default timezone set to IST");
    }
}

int indicator_time_init(void)
{
    __g_data_mutex  =  xSemaphoreCreateMutex();

    memset(__g_time_model.net_zone, 0 , sizeof(__g_time_model.net_zone));

    __time_cfg_restore();

    sntp_setoperatingmode(SNTP_OPMODE_POLL);
    sntp_setservername(0, "pool.ntp.org");
    sntp_setservername(1, "cn.ntp.org.cn");
    sntp_set_time_sync_notification_cb(__time_sync_notification_cb);
    
    struct view_data_time_cfg cfg;
    __time_cfg_get(&cfg);
    __time_cfg(&cfg, true);
    esp_event_post_to(view_event_handle, VIEW_EVENT_BASE, VIEW_EVENT_TIME_CFG_UPDATE, &cfg, sizeof(cfg), portMAX_DELAY);

    __time_view_update_init();

    ESP_ERROR_CHECK(esp_event_handler_instance_register_with(view_event_handle, 
                                                            VIEW_EVENT_BASE, VIEW_EVENT_TIME_CFG_APPLY, 
                                                            __view_event_handler, NULL, NULL));
    ESP_ERROR_CHECK(esp_event_handler_instance_register_with(view_event_handle, 
                                                            VIEW_EVENT_BASE, VIEW_EVENT_WIFI_ST, 
                                                            __view_event_handler, NULL, NULL));
}

int indicator_time_net_zone_set( char *p)
{
    // If IST is forced, ignore timezone changes from other sources
    if (force_ist_timezone) {
        ESP_LOGI(TAG, "IST timezone forced, ignoring timezone change to: %s", p);
        return 0;
    }

    xSemaphoreTake(__g_data_mutex, portMAX_DELAY);
    strcpy(__g_time_model.net_zone, p);
    xSemaphoreGive(__g_data_mutex);

    struct view_data_time_cfg cfg;
    __time_cfg_get(&cfg);

    if( cfg.auto_update ) {
        __time_zone_set(&cfg);
    }
    bool time_format_24 = cfg.time_format_24;
    esp_event_post_to(view_event_handle, VIEW_EVENT_BASE, VIEW_EVENT_TIME, &time_format_24, sizeof(time_format_24), portMAX_DELAY);
    return 0;
}

int indicator_time_set_ist(void)
{
    int ret = __set_ist_time();
    if (ret == 0) {
        // Update the net_zone to IST
        xSemaphoreTake(__g_data_mutex, portMAX_DELAY);
        strcpy(__g_time_model.net_zone, "IST-5:30");
        xSemaphoreGive(__g_data_mutex);

        // Trigger time display update
        struct view_data_time_cfg cfg;
        __time_cfg_get(&cfg);
        bool time_format_24 = cfg.time_format_24;
        esp_event_post_to(view_event_handle, VIEW_EVENT_BASE, VIEW_EVENT_TIME, &time_format_24, sizeof(time_format_24), portMAX_DELAY);
    }
    return ret;
}

void indicator_time_force_ist(bool enable)
{
    force_ist_timezone = enable;
    ESP_LOGI(TAG, "IST timezone forcing %s", enable ? "enabled" : "disabled");
}
